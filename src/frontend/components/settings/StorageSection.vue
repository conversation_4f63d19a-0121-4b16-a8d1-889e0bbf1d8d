<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Storage configuration</h2>
      
      <!-- Free Plan Limitations - Only show for free users -->
      <div v-if="!isProUser" class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
        <div class="space-y-2 text-sm text-base-content/70">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Maximum attachment size: <strong>128KB</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Text-based files only: <strong>PDF, TXT, CSV, MD, ICS, JSON, XML</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Attachments included as <strong>base64</strong> in webhook payload</span>
          </div>
        </div>
      </div>

      <!-- Attachment Processing Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">
          <span v-if="!isProUser" class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline mr-1">Pro</span>
          Attachment processing
        </h3>
        <p v-if="!isProUser" class="text-sm text-base-content/70 mb-6">Upgrade to Pro to unlock advanced attachment handling</p>
        <p v-else class="text-sm text-base-content/70 mb-6">Configure how attachments are processed and stored</p>

        <div class="space-y-6" :class="{ 'opacity-60': !isProUser }">
          <!-- Max File Size Slider -->
          <div>
            <label class="label">
              <span class="label-text">Maximum attachment size</span>
            </label>
            <div class="flex items-center space-x-4">
              <input
                type="range"
                min="0.1"
                max="10"
                step="0.1"
                v-model="settings.maxInlineSize"
                :disabled="!isProUser"
                class="range range-primary flex-1"
              >
              <span class="text-sm font-medium min-w-[4rem]">{{ settings.maxInlineSize }} MB</span>
            </div>
          </div>

          <!-- Allow Media Files Toggle -->
          <div>
            <label class="label cursor-pointer justify-start">
              <input
                type="checkbox"
                v-model="settings.allowMediaFiles"
                :disabled="!isProUser"
                class="checkbox checkbox-primary mr-3"
              />
              <div class="flex flex-col">
                <span class="label-text">Allow media files</span>
                <span class="label-text-alt text-xs">Process images, videos, and other media attachments</span>
              </div>
            </label>
          </div>

          <!-- Storage Provider -->
          <div>
            <label class="label">
              <span class="label-text">Storage provider</span>
            </label>
            <select
              v-model="settings.storageProvider"
              :disabled="!isProUser"
              class="select select-bordered w-full"
            >
              <option value="default">{{ isProUser ? 'Default (EmailConnect storage)' : 'No external storage (Free plan)' }}</option>
              <option v-if="isProUser" value="s3">Amazon S3</option>
              <option v-if="isProUser" value="s3-compatible">S3-Compatible Storage</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                {{ isProUser ? 'Configure any S3-compatible storage for large attachments' : 'Upgrade to Pro to use external storage' }}
              </span>
            </label>
          </div>

          <!-- Retention Policy -->
          <div>
            <label class="label">
              <span class="label-text">Attachment retention</span>
            </label>
            <select
              v-model="settings.retentionHours"
              :disabled="!isProUser"
              class="select select-bordered w-full"
            >
              <option :value="1">{{ isProUser ? '1 hour' : 'No retention options (Free plan)' }}</option>
              <option v-if="isProUser" :value="24">24 hours</option>
              <option v-if="isProUser" :value="168">7 days</option>
              <option v-if="isProUser" :value="720">30 days</option>
            </select>
          </div>

          <!-- Save Button -->
          <div class="pt-4">
            <button
              type="button"
              @click="saveSettings"
              :disabled="!isProUser || saving"
              class="btn btn-primary"
            >
              {{ saving ? 'Saving...' : 'Save storage settings' }}
            </button>
            <div v-if="!isProUser" class="text-sm text-base-content/60 mt-2">
              <router-link to="/settings#billing" class="link">Upgrade to Pro</router-link> to configure storage settings
            </div>
          </div>
        </div>
      </div>

      <!-- Future Features Preview -->
      <div class="bg-base-200/40 rounded-lg p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Coming soon</h3>
        <div class="space-y-3">
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Custom S3 bucket configuration</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Domain-specific storage settings</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Alias-level attachment policies</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Advanced retention policies</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useMetrics } from '../../composables/useMetrics'

// Composables
const { metricsData, loadMetrics } = useMetrics()

// State
const saving = ref(false)
const settings = ref({
  maxInlineSize: 1.0,
  allowMediaFiles: false,
  storageProvider: 'default',
  retentionHours: 1
})

// Check if user has Pro plan or higher
const isProUser = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  return planType === 'pro' || planType === 'enterprise'
})

// Methods

const saveSettings = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving storage settings:', settings.value)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message
    alert('Storage settings saved successfully!')
  } catch (error) {
    console.error('Failed to save storage settings:', error)
    alert('Failed to save settings. Please try again.')
  } finally {
    saving.value = false
  }
}

onMounted(async () => {
  // Load user metrics to determine plan type
  await loadMetrics()
})
</script>

<style scoped>
/* Storage section specific styles */
.stat {
  padding: 1rem;
}
</style>
