<template>
  <header class="bg-base-100 border-b border-base-300">
    <div class="flex items-center justify-between h-16 px-6 mx-auto max-w-7xl lg:px-8">
      <div class="flex items-center space-x-4">
        <router-link to="/domains" class="flex items-center space-x-2">
          <Logo :show-text="false" :clickable="false" />
        </router-link>
      </div>

      <div class="flex-none flex items-center gap-4">
        <!-- Notifications -->
        <NotificationBell />

        <!-- Theme Toggle -->
        <ThemeToggle />

        <!-- User Dropdown -->
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
            <div class="w-10 rounded-full">
              <div class="flex items-center justify-center w-10 h-10 bg-primary text-primary-content rounded-full">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
            <li class="menu-title">
              <div class="inline-flex items-center gap-2">
                <span>{{ user?.name || user?.email || 'User' }}</span>
                <span
                  v-if="user?.planType"
                  class="badge badge-sm"
                  :class="planBadgeClass"
                >
                  {{ planDisplayName }}
                </span>
              </div>
            </li>
            <li>
              <router-link to="/settings" class="flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span class="text-base">Settings</span>
              </router-link>
            </li>
            <li>
              <a
                href="mailto:<EMAIL>?subject=Support"
                class="flex items-center gap-2"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-1.106A9.723 9.723 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8c0 1.063-.229 2.077-.602 3.001m0 0H21"
                  />
                </svg>
                <span class="text-base">Contact</span>
              </a>
            </li>
            <li>
              <button @click="logout" class="flex items-center w-full gap-2 text-left">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span class="text-base">Logout</span>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMetrics } from '../../composables/useMetrics'
import { useNotifications } from '../../composables/useNotifications'
import ThemeToggle from '../ui/ThemeToggle.vue'
import Logo from '../ui/Logo.vue'
import NotificationBell from '../ui/NotificationBell.vue'

// Types
interface User {
  id: string
  email: string
  name?: string
  planType?: string
}

// Vue Router
const router = useRouter()

// Use shared metrics composable instead of duplicate API call
const { metricsData, loadMetrics } = useMetrics()

// Initialize notifications for the app
const { initializeNotifications } = useNotifications()

// State
const user = ref<User | null>(null)

// Computed properties for plan badge
const planDisplayName = computed(() => {
  const planType = user.value?.planType || 'free'
  switch (planType.toLowerCase()) {
    case 'pro':
      return 'Pro'
    case 'enterprise':
      return 'Enterprise'
    case 'free':
    default:
      return 'Free'
  }
})

const planBadgeClass = computed(() => {
  const planType = user.value?.planType || 'free'
  switch (planType.toLowerCase()) {
    case 'pro':
      return 'badge-primary badge-outline'
    case 'enterprise':
      return 'badge-secondary badge-outline'
    case 'free':
    default:
      return 'badge-neutral'
  }
})

// Load user data - now uses shared metrics data
const loadUserData = async () => {
  try {
    // Use shared metrics data instead of separate API call
    await loadMetrics()

    // Use actual user data from response if available
    if (metricsData.value?.user) {
      user.value = metricsData.value.user
    } else {
      // Set fallback user data
      user.value = { id: 'user', email: 'User', name: 'User' }
    }
  } catch (error) {
    console.error('Failed to load user data:', error)
    // Set fallback user data
    user.value = { id: 'user', email: 'User', name: 'User' }
  }
}

// Navigate to dashboard
const navigateToDashboard = () => {
  router.push('/domains')
}

// Logout function
const logout = async () => {
  try {
    const response = await fetch('/logout', {
      method: 'POST',
      credentials: 'include'
    })

    if (response.ok) {
      // Redirect to login
      // httpOnly cookie is cleared by the server
      window.location.href = '/login'
    } else {
      console.error('Logout failed')
    }
  } catch (error) {
    console.error('Logout error:', error)
  }
}

onMounted(() => {
  loadUserData()
  // Initialize notifications when header loads (user is authenticated)
  initializeNotifications()
})
</script>

<style scoped>
/* User header specific styles */
</style>
