<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useForm } from '../../composables/useForm'
import { useAliasApi, useDomainApi } from '../../composables/useApi'
import { useDataRefresh } from '../../composables/useDataRefresh'
import { useMetrics } from '../../composables/useMetrics'
import { usePermissions } from '../../composables/usePermissions'
import WebhookSelector from './WebhookSelector.vue'
import type { CreateAliasRequest, Domain, Webhook } from '../../types'

interface Props {
  initialData?: Partial<CreateAliasRequest & {
    id?: string
    allowAttachments?: boolean
    includeEnvelope?: boolean
    isCatchAll?: boolean
    attachmentHandling?: 'inline' | 'storage'
    s3Folder?: string
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [alias: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateAliasRequest & {
  allowAttachments?: boolean
  includeEnvelope?: boolean
  attachmentHandling?: 'inline' | 'storage'
  s3Folder?: string
}>({
  email: props.initialData.email || '',
  domainId: props.initialData.domainId || '',
  webhookId: props.initialData.webhookId || '',
  active: props.initialData.active ?? true,
  allowAttachments: props.initialData.allowAttachments ?? false,
  includeEnvelope: props.initialData.includeEnvelope ?? true,
  attachmentHandling: props.initialData.attachmentHandling || 'inline',
  s3Folder: props.initialData.s3Folder || ''
}, [
  { name: 'email', label: 'Email alias', type: 'email', required: true },
  { name: 'domainId', label: 'Domain', type: 'select', required: true },
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true },
  { name: 'allowAttachments', label: 'Allow attachments', type: 'checkbox', required: false },
  { name: 'includeEnvelope', label: 'Include envelope data', type: 'checkbox', required: false },
  { name: 'attachmentHandling', label: 'Attachment handling', type: 'select', required: false },
  { name: 's3Folder', label: 'S3 Folder', type: 'text', required: false }
])

// API setup
const { createAlias, updateAlias } = useAliasApi()
const { getDomains } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics, metricsData } = useMetrics()
const { hasPermission, loadPermissions } = usePermissions()

// State
const domains = ref<Domain[]>([])
const loadingDomains = ref(false)
const aliasPrefix = ref('')
const s3BucketName = ref('my-webhook-mails') // This would come from user's storage settings

// Computed
const selectedDomain = computed(() =>
  domains.value.find(d => d.id === values.domainId)
)

const fullEmail = computed(() => {
  if (aliasPrefix.value && selectedDomain.value) {
    return `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`
  }
  return ''
})

// Check if user is Pro (for attachment features)
const isProUser = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  return planType === 'pro' || planType === 'enterprise'
})

// Check if user can use attachment storage
const canUseAttachments = computed(() => {
  return isProUser.value
})

// Methods
const loadDomains = async () => {
  loadingDomains.value = true
  try {
    const response = await getDomains() as any
    domains.value = response.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    loadingDomains.value = false
  }
}

const onWebhookCreated = (webhook: Webhook) => {
  // The WebhookSelector component will automatically select the new webhook
}

// Initialize alias prefix based on initial data
const initializeAliasPrefix = () => {
  if (props.initialData.email) {
    if (props.initialData.isCatchAll) {
      aliasPrefix.value = 'catch-all'
    } else if (props.initialData.email.includes('@')) {
      aliasPrefix.value = props.initialData.email.split('@')[0]
    } else {
      aliasPrefix.value = props.initialData.email
    }
  }
}

const updateEmail = () => {
  if (props.isEditMode && props.initialData.isCatchAll) {
    // Don't update email for catch-all aliases in edit mode
    return
  }

  if (aliasPrefix.value && selectedDomain.value) {
    setFieldValue('email', `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`)
  }
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing alias
      const updateData = {
        email: formData.email,
        webhookId: formData.webhookId,
        allowAttachments: formData.allowAttachments,
        includeEnvelope: formData.includeEnvelope,
        attachmentHandling: formData.attachmentHandling,
        s3Folder: formData.s3Folder
      }
      result = await updateAlias(props.initialData.id, updateData)
      emit('success', result.alias)
      refreshWithSuccess('Alias updated successfully!', 'aliases')
    } else {
      // Create mode - create new alias
      result = await createAlias(formData) as any

      // Check if webhook needs verification
      if (result.webhookNeedsVerification) {
        // Show success message with webhook verification guidance
        const message = result.aliasSetInactive
          ? 'Alias created but set to inactive. Please verify the webhook to activate and start receiving emails.'
          : 'Alias created successfully! Please verify the webhook to ensure reliable email delivery.'

        refreshWithSuccess(message, 'aliases')

        // Emit success with webhook verification info
        emit('success', {
          ...result.alias,
          webhookNeedsVerification: true,
          webhook: result.webhook
        })
      } else {
        // Standard success flow
        emit('success', result.alias)
        refreshWithSuccess('Alias created successfully!', 'aliases')
      }
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after alias operation:', error)
      }
    }, 500)
  })
}

onMounted(async () => {
  loadDomains()
  initializeAliasPrefix()
  // Load permissions for Pro feature checking
  await loadPermissions()
  // WebhookSelector component handles loading webhooks
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Email Alias -->
    <div class="form-control">
      <label class="flex-col items-start label">
        <span class="text-gray-500 label-text-alt">Alias</span>
      </label>
      <div class="relative flex items-center min-w-0 bg-base-100 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
        <input
          v-model="aliasPrefix"
          @input="updateEmail"
          type="text"
          autocapitalize="none"
          spellcheck="false"
          :placeholder="props.initialData.isCatchAll ? 'catch-all' : 'support'"
          class="w-1/3 sm:w-1/2 min-w-0 px-3 py-2 text-sm text-base-content bg-transparent border-0 placeholder:text-base-content/40 focus:outline-none focus:ring-0"
          :disabled="isEditMode"
          :readonly="props.isEditMode && props.initialData.isCatchAll"
          required
        />
        <div class="px-2 text-sm text-base-content/70 select-none">@</div>
        <select
          :value="values.domainId"
          @change="setFieldValue('domainId', ($event.target as HTMLSelectElement).value); updateEmail()"
          class="flex-1 min-w-0 py-2 pl-3 pr-8 text-sm text-base-content bg-transparent border-0 appearance-none cursor-pointer focus:outline-none focus:ring-0"
          :disabled="loadingDomains || isEditMode"
          required
        >
          <option value="">
            {{ loadingDomains ? 'Loading...' : 'Select domain...' }}
          </option>
          <option
            v-for="domain in domains"
            :key="domain.id"
            :value="domain.id"
          >
            {{ domain.domainName || domain.domain }}
          </option>
        </select>
        <svg class="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 pointer-events-none right-2 top-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      <div v-if="errors.email" class="label">
        <span class="label-text-alt text-error">{{ errors.email }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">A unique alias to forward emails to your webhook</span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-alias"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Configuration Options -->
    <div class="bg-base-200/40 rounded-lg p-4 space-y-4">
      <div class="flex items-center gap-2 mb-2">
        <h3 class="text-sm font-semibold text-base-content/80">Advanced configuration</h3>
        <span v-if="!isProUser" class="badge badge-primary badge-outline badge-sm">Pro</span>
      </div>

      <!-- Allow Attachments -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.allowAttachments"
            @change="setFieldValue('allowAttachments', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Allow attachments</span>
            <span class="label-text-alt text-xs">Process email attachments inline or store in bucket</span>
          </div>
        </label>
      </div>

      <!-- Attachment Handling Options (only show when attachments enabled) -->
      <div v-if="values.allowAttachments" class="ml-6 pl-4 space-y-3">
        <!-- Attachment Handling Choice - Card Layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <!-- Inline (Base64) Option -->
          <div
            @click="setFieldValue('attachmentHandling', 'inline')"
            class="card bg-base-100 border-2 cursor-pointer transition-all hover:shadow-md"
            :class="values.attachmentHandling === 'inline' ? 'border-primary ring-2 ring-primary/20' : 'border-base-300 hover:border-primary/50'"
          >
            <div class="card-body p-4">
              <h4 class="card-title text-sm">Include inline (Base64)</h4>
              <div class="text-xs text-base-content/70 space-y-1">
                <p>Encoded directly in webhook payload</p>
                <p>• Immediate access</p>
                <p>• Limited to 128KB total</p>
              </div>
            </div>
          </div>

          <!-- S3 Storage Option (Pro only) -->
          <div
            @click="canUseAttachments ? setFieldValue('attachmentHandling', 'storage') : null"
            class="card bg-base-100 border-2 transition-all"
            :class="[
              values.attachmentHandling === 'storage' && canUseAttachments ? 'border-primary ring-2 ring-primary/20' : 'border-base-300',
              canUseAttachments ? 'cursor-pointer hover:shadow-md hover:border-primary/50' : 'cursor-not-allowed opacity-60'
            ]"
          >
            <div class="card-body p-4">
              <div class="flex items-center gap-2 mb-2">
                <h4 class="card-title text-sm">Store in S3 bucket</h4>
                <span class="badge badge-primary badge-outline badge-xs">Pro</span>
              </div>
              <div class="text-xs text-base-content/70 space-y-1">
                <template v-if="canUseAttachments">
                  <p>Stored separately, URLs in payload</p>
                  <p>• No size limits</p>
                  <p>• Requires <router-link to="/settings#storage" class="link link-primary">S3 configuration</router-link></p>
                </template>
                <template v-else>
                  <p>Upgrade to Pro to enable S3 storage</p>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- S3 Storage Configuration (only show when storage option selected and Pro) -->
      <div v-if="values.allowAttachments && values.attachmentHandling === 'storage' && canUseAttachments" class="ml-6 pl-4 border-l-2 border-primary/20 space-y-3">
        <!-- S3 Bucket and Folder Configuration -->
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">Storage location</span>
          </label>
          <div class="flex items-center gap-0 bg-base-100 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
            <!-- S3 Bucket (readonly) -->
            <div class="flex items-center px-3 py-2 bg-base-200/50 border-r border-base-300 rounded-l-lg">
              <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0a2 2 0 002 2v0" />
              </svg>
              <span class="text-sm text-base-content/70 font-mono">{{ s3BucketName }}</span>
            </div>
            <!-- Folder Path Input -->
            <input
              :value="values.s3Folder"
              @input="setFieldValue('s3Folder', ($event.target as HTMLInputElement).value)"
              type="text"
              placeholder="alias-folder-name"
              class="flex-1 px-3 py-2 text-sm bg-transparent border-0 focus:outline-none focus:ring-0"
              pattern="^[a-zA-Z0-9\-_/]*$"
            />
          </div>
          <div class="label">
            <span class="label-text-alt text-xs">
              Attachments will be stored in: <code class="text-primary">{{ s3BucketName }}/{{ values.s3Folder || 'alias-folder-name' }}</code>
            </span>
          </div>
        </div>
      </div>

      <!-- Include Envelope -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.includeEnvelope"
            @change="setFieldValue('includeEnvelope', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Include envelope data</span>
            <span class="label-text-alt text-xs">Include email headers and metadata in webhook payload</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update alias' : 'Create alias')
        }}
      </button>
    </div>
  </form>
</template>