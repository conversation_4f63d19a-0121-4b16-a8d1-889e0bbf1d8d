import { prisma } from '../../lib/prisma.js';
import { PlanConfigService } from './plan-config.service.js';
import { CreditService } from './credit.service.js';

export interface UsageInfo {
  // Current usage
  currentUsage: {
    emails: number;
    domains: number;
    aliases: number;
    webhooks: number;
  };
  
  // Limits
  limits: {
    emails: number;
    domains: number;
    aliases: number;
    webhooks: number;
  };
  
  // Email breakdown
  emailBreakdown: {
    monthlyAllowance: number;
    monthlyUsed: number;
    monthlyRemaining: number;
    purchasedCredits: number;
    totalAvailable: number;
  };
  
  // Credit info
  creditInfo: {
    balance: number;
    expiringCredits: number;
    nextExpirationDate: Date | null;
  };
}

export interface EmailUsageResult {
  success: boolean;
  source: 'monthly' | 'credits';
  remainingMonthly: number;
  remainingCredits: number;
  error?: string;
}

export class UsageCalculationService {
  /**
   * Get comprehensive usage information for a user
   */
  static async getUserUsageInfo(userId: string): Promise<UsageInfo> {
    // Get user with plan info
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        planType: true,
        monthlyEmailLimit: true,
        currentMonthEmails: true,
        domains: { select: { id: true } },
        webhooks: { select: { id: true } },
        _count: {
          select: {
            domains: true,
            webhooks: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get alias count across all domains
    const aliasCount = await prisma.alias.count({
      where: {
        domain: {
          userId
        }
      }
    });

    // Get plan limits (domain-based for Pro)
    const limits = PlanConfigService.getPlanLimits(user.planType, user._count.domains);

    // Get credit balance
    const creditBalance = await CreditService.getCreditBalance(userId);

    // Calculate email breakdown using plan-based monthly limit instead of stored value
    const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
    const monthlyAllowance = planConfig.monthlyEmailLimit;
    const monthlyUsed = user.currentMonthEmails;
    const monthlyRemaining = Math.max(0, monthlyAllowance - monthlyUsed);
    const totalAvailable = monthlyRemaining + creditBalance.totalCredits;

    return {
      currentUsage: {
        emails: monthlyUsed,
        domains: user._count.domains,
        aliases: aliasCount,
        webhooks: user._count.webhooks
      },
      limits,
      emailBreakdown: {
        monthlyAllowance: monthlyAllowance,
        monthlyUsed,
        monthlyRemaining,
        purchasedCredits: creditBalance.totalCredits,
        totalAvailable
      },
      creditInfo: {
        balance: creditBalance.totalCredits,
        expiringCredits: creditBalance.expiringCredits,
        nextExpirationDate: creditBalance.nextExpirationDate
      }
    };
  }

  /**
   * Process email usage - use monthly allowance first, then credits
   */
  static async processEmailUsage(userId: string, emailCount: number = 1): Promise<EmailUsageResult> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        planType: true,
        currentMonthEmails: true
      }
    });

    if (!user) {
      return {
        success: false,
        source: 'monthly',
        remainingMonthly: 0,
        remainingCredits: 0,
        error: 'User not found'
      };
    }

    // Use plan-based monthly limit instead of stored value
    const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
    const monthlyAllowance = planConfig.monthlyEmailLimit;
    const monthlyRemaining = monthlyAllowance - user.currentMonthEmails;

    // Try to use monthly allowance first
    if (monthlyRemaining >= emailCount) {
      // Use monthly allowance
      await prisma.user.update({
        where: { id: userId },
        data: {
          currentMonthEmails: { increment: emailCount }
        }
      });

      const creditBalance = await CreditService.getCreditBalance(userId);

      return {
        success: true,
        source: 'monthly',
        remainingMonthly: monthlyRemaining - emailCount,
        remainingCredits: creditBalance.totalCredits
      };
    }

    // Monthly allowance exhausted, try to use credits
    const creditResult = await CreditService.useCredits(userId, emailCount);
    
    if (!creditResult.success) {
      return {
        success: false,
        source: 'credits',
        remainingMonthly: monthlyRemaining,
        remainingCredits: creditResult.remainingCredits,
        error: creditResult.error
      };
    }

    return {
      success: true,
      source: 'credits',
      remainingMonthly: monthlyRemaining,
      remainingCredits: creditResult.remainingCredits
    };
  }

  /**
   * Check if user can process emails (has monthly allowance or credits)
   */
  static async canProcessEmails(userId: string, emailCount: number = 1): Promise<boolean> {
    const usageInfo = await this.getUserUsageInfo(userId);
    return usageInfo.emailBreakdown.totalAvailable >= emailCount;
  }

  /**
   * Get usage summary for billing display
   */
  static async getBillingUsageSummary(userId: string) {
    const usageInfo = await this.getUserUsageInfo(userId);
    
    return {
      usage: usageInfo.currentUsage,
      limits: usageInfo.limits,
      emailBreakdown: usageInfo.emailBreakdown,
      creditInfo: usageInfo.creditInfo
    };
  }

  /**
   * Get simple usage for MetricsPill display
   */
  static async getSimpleUsage(userId: string) {
    const usageInfo = await this.getUserUsageInfo(userId);
    
    return {
      emails: usageInfo.currentUsage.emails,
      domains: usageInfo.currentUsage.domains,
      aliases: usageInfo.currentUsage.aliases,
      webhooks: usageInfo.currentUsage.webhooks,
      emailsAvailable: usageInfo.emailBreakdown.totalAvailable,
      emailsLimit: usageInfo.limits.emails
    };
  }
}
